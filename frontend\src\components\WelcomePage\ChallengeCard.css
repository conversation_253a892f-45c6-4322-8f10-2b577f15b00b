/* ChallengeCard.css */

.challenge-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    position: relative;
    border: 1px solid transparent;
}

.challenge-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-green);
}

.challenge-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--light-gray);
    background: linear-gradient(135deg, var(--light-gray) 0%, #f8f9fa 100%);
}

.challenge-badge {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.badge {
    padding: 4px 8px;
    border-radius: var(--border-radius);
    font-size: 11px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge.new {
    background: var(--primary-green);
    color: var(--white);
    box-shadow: 0 2px 4px rgba(11, 90, 39, 0.3);
}

.badge.popular {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--white);
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.badge.hot {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: var(--white);
    box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);
    animation: pulse 2s infinite;
}

.badge.vip {
    background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
    color: var(--dark-gray);
    box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
    font-weight: 800;
}

.time-left {
    font-size: 12px;
    color: var(--gray);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.time-left::before {
    content: '⏰';
    font-size: 10px;
}

.challenge-content {
    padding: 20px;
}

.challenge-content h3 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--dark-gray);
    line-height: 1.3;
}

.challenge-content p {
    color: var(--gray);
    margin-bottom: 20px;
    line-height: 1.5;
    font-size: 14px;
}

.challenge-stats {
    display: flex;
    justify-content: space-between;
    gap: 15px;
    margin-bottom: 20px;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex: 1;
}

.stat .label {
    font-size: 11px;
    color: var(--gray);
    text-transform: uppercase;
    margin-bottom: 4px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.stat .value {
    font-weight: 700;
    font-size: 14px;
    color: var(--dark-gray);
}

.stat .value.prize {
    color: var(--gold);
    font-size: 16px;
    text-shadow: 0 1px 2px rgba(255, 215, 0, 0.3);
}

.stat .value.progress {
    color: var(--primary-green);
    font-weight: 800;
}

.challenge-progress {
    margin-bottom: 15px;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: var(--light-gray);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-green) 0%, var(--secondary-green) 100%);
    border-radius: 3px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    font-size: 12px;
    color: var(--gray);
    font-weight: 500;
}

.challenge-footer {
    padding: 15px 20px;
    background: var(--light-gray);
    border-top: 1px solid #e9ecef;
}

.participate-btn {
    width: 100%;
    background: linear-gradient(135deg, var(--primary-green) 0%, var(--secondary-green) 100%);
    color: var(--white);
    border: none;
    padding: 12px;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.participate-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.participate-btn:hover::before {
    left: 100%;
}

.participate-btn:hover {
    background: linear-gradient(135deg, var(--secondary-green) 0%, var(--accent-green) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(11, 90, 39, 0.3);
}

.participate-btn:active {
    transform: translateY(0);
}

.challenge-decoration {
    position: absolute;
    top: 0;
    right: 0;
    pointer-events: none;
    opacity: 0.1;
}

.decoration-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-green);
    position: absolute;
    top: -30px;
    right: -30px;
}

.decoration-triangle {
    width: 0;
    height: 0;
    border-left: 20px solid transparent;
    border-right: 20px solid transparent;
    border-bottom: 20px solid var(--secondary-green);
    position: absolute;
    top: 10px;
    right: 10px;
    transform: rotate(45deg);
}

/* Responsive Design */
@media (max-width: 768px) {
    .challenge-content {
        padding: 15px;
    }
    
    .challenge-content h3 {
        font-size: 16px;
    }
    
    .challenge-stats {
        gap: 10px;
    }
    
    .stat .value {
        font-size: 13px;
    }
    
    .stat .value.prize {
        font-size: 14px;
    }
    
    .participate-btn {
        padding: 10px;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .challenge-header {
        padding: 12px 15px;
    }
    
    .challenge-content {
        padding: 12px;
    }
    
    .challenge-content h3 {
        font-size: 15px;
        margin-bottom: 8px;
    }
    
    .challenge-content p {
        font-size: 13px;
        margin-bottom: 15px;
    }
    
    .challenge-stats {
        flex-direction: column;
        gap: 8px;
        margin-bottom: 15px;
    }
    
    .stat {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }
    
    .stat .label {
        margin-bottom: 0;
        text-align: left;
    }
    
    .challenge-footer {
        padding: 12px 15px;
    }
    
    .participate-btn {
        padding: 10px;
        font-size: 12px;
    }
}
