@tailwind base;
@tailwind components;
@tailwind utilities;

/* ===== CONSOLIDATED CSS RESET ===== */
/* Single source of truth for all reset styles */
html, body {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
  height: auto;
  overflow-x: hidden;
  overflow-y: auto;
  scroll-behavior: smooth;
}

*, *::before, *::after {
  box-sizing: inherit;
}

/* ===== CSS CUSTOM PROPERTIES (DESIGN SYSTEM) ===== */
:root {
  /* Primary Colors */
  --primary-color: #166534;
  --primary-hover: #15803d;
  --primary-light: #22c55e;
  --primary-dark: #14532d;

  /* Secondary Colors */
  --secondary-color: #2563eb;
  --secondary-hover: #1d4ed8;

  /* Status Colors */
  --success-color: #059669;
  --success-light: #d1fae5;
  --warning-color: #d97706;
  --warning-light: #fef3c7;
  --danger-color: #dc2626;
  --danger-light: #fee2e2;
  --info-color: #0891b2;
  --info-light: #cffafe;

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #f3f4f6;
  --bg-dark: #1f2937;
  --bg-darker: #111827;

  /* Text Colors */
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --text-inverse: #ffffff;

  /* Border Colors */
  --border-light: #e5e7eb;
  --border-medium: #d1d5db;
  --border-dark: #9ca3af;

  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);

  /* Spacing Scale */
  --space-xs: 0.25rem;   /* 4px */
  --space-sm: 0.5rem;    /* 8px */
  --space-md: 0.75rem;   /* 12px */
  --space-lg: 1rem;      /* 16px */
  --space-xl: 1.5rem;    /* 24px */
  --space-2xl: 2rem;     /* 32px */
  --space-3xl: 3rem;     /* 48px */

  /* Border Radius */
  --radius-sm: 0.25rem;  /* 4px */
  --radius-md: 0.375rem; /* 6px */
  --radius-lg: 0.5rem;   /* 8px */
  --radius-xl: 0.75rem;  /* 12px */
  --radius-2xl: 1rem;    /* 16px */

  /* Z-index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;

  /* Responsive Breakpoints */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* ===== UNIFIED RESPONSIVE TABLE SYSTEM ===== */
/* Standardized responsive behavior for all admin tables */

/* Large screens (1366px+) - Full table display */
@media (min-width: 1366px) {
  .admin-table-responsive .table-hide-large {
    display: table-cell;
  }
  .admin-table-responsive .table-hide-medium,
  .admin-table-responsive .table-hide-small {
    display: table-cell;
  }
}

/* Medium screens (1024px - 1365px) - Hide less important columns */
@media (min-width: 1024px) and (max-width: 1365px) {
  .admin-table-responsive .table-hide-medium {
    display: none;
  }
  .admin-table-responsive .table-hide-large {
    display: table-cell;
  }
  .admin-table-responsive .table-hide-small {
    display: table-cell;
  }
}

/* Small screens (768px - 1023px) - Hide more columns */
@media (min-width: 768px) and (max-width: 1023px) {
  .admin-table-responsive .table-hide-small,
  .admin-table-responsive .table-hide-medium {
    display: none;
  }
  .admin-table-responsive .table-hide-large {
    display: table-cell;
  }
}

/* Mobile screens (below 768px) - Card layout */
@media (max-width: 767px) {
  .admin-table-responsive {
    display: block;
  }

  .admin-table-responsive table,
  .admin-table-responsive thead,
  .admin-table-responsive tbody,
  .admin-table-responsive th,
  .admin-table-responsive td,
  .admin-table-responsive tr {
    display: block;
  }

  .admin-table-responsive thead tr {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }

  .admin-table-responsive tr {
    border: 1px solid var(--border-light);
    margin-bottom: var(--space-md);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    padding: var(--space-md);
  }

  .admin-table-responsive td {
    border: none;
    position: relative;
    padding-left: 50%;
    padding-top: var(--space-sm);
    padding-bottom: var(--space-sm);
  }

  .admin-table-responsive td:before {
    content: attr(data-label) ": ";
    position: absolute;
    left: 6px;
    width: 45%;
    padding-right: 10px;
    white-space: nowrap;
    font-weight: 600;
    color: var(--text-secondary);
  }
}

/* ===== UNIFIED MODAL SYSTEM ===== */
/* Single source of truth for all modal styles */

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: var(--z-modal-backdrop);
  backdrop-filter: blur(4px);
  animation: modalOverlayFadeIn 0.2s ease-out;
}

@keyframes modalOverlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal-content {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  animation: modalContentSlideIn 0.3s ease-out;
}

@keyframes modalContentSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-xl) var(--space-xl) var(--space-lg) var(--space-xl);
  border-bottom: 1px solid var(--border-light);
  background-color: var(--bg-tertiary);
  border-top-left-radius: var(--radius-xl);
  border-top-right-radius: var(--radius-xl);
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.modal-body {
  padding: var(--space-xl);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-md);
  padding: var(--space-lg) var(--space-xl) var(--space-xl) var(--space-xl);
  border-top: 1px solid var(--border-light);
  background-color: var(--bg-tertiary);
  border-bottom-left-radius: var(--radius-xl);
  border-bottom-right-radius: var(--radius-xl);
}

.close-modal,
.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--space-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
  width: 32px;
  height: 32px;
}

.close-modal:hover,
.close-button:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Modal Size Variants */
.modal-content.modal-sm {
  max-width: 400px;
}

.modal-content.modal-md {
  max-width: 600px;
}

.modal-content.modal-lg {
  max-width: 900px;
}

.modal-content.modal-xl {
  max-width: 1200px;
}

/* Full Screen Modal for Mobile */
@media (max-width: 768px) {
  .modal-content {
    width: 100%;
    height: 100%;
    max-width: none;
    max-height: none;
    border-radius: 0;
    animation: modalContentSlideUp 0.3s ease-out;
  }

  @keyframes modalContentSlideUp {
    from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
  }

  .modal-header,
  .modal-footer {
    border-radius: 0;
  }
}

/* ===== UNIFIED LOADING SPINNER SYSTEM ===== */
/* Single source of truth for all loading animations */

.loading-spinner {
  border: 3px solid var(--border-light);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Loading Spinner Variants */
.loading-spinner.spinner-sm {
  width: 16px;
  height: 16px;
  border-width: 2px;
}

.loading-spinner.spinner-lg {
  width: 48px;
  height: 48px;
  border-width: 4px;
}

.loading-spinner.spinner-xl {
  width: 64px;
  height: 64px;
  border-width: 5px;
}

/* Loading Spinner Colors */
.loading-spinner.spinner-primary {
  border-top-color: var(--primary-color);
}

.loading-spinner.spinner-secondary {
  border-top-color: var(--secondary-color);
}

.loading-spinner.spinner-success {
  border-top-color: var(--success-color);
}

.loading-spinner.spinner-danger {
  border-top-color: var(--danger-color);
}

/* Loading Container */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--space-2xl);
  flex-direction: column;
  gap: var(--space-lg);
}

.loading-text {
  color: var(--text-secondary);
  font-size: 0.875rem;
  text-align: center;
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #38a169;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background: #48bb78;
}

#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Apply dark background only to user/admin layouts, not homepage */
.user-layout,
.admin-layout {
  background-color: #13141B;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}
