/* AdminLayout.css - Using CSS variables from index.css */

/* Main layout container */
.admin-layout {
  display: flex;
  min-height: 100vh;
  background-color: var(--bg-secondary);
  overflow-x: hidden;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--border-medium) var(--bg-tertiary);
}

/* Main wrapper - adjust margin based on sidebar width */
.main-wrapper {
  flex: 1;
  margin-left: 320px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  transition: margin-left 0.3s ease;
}

/* Content area */
.content {
  flex: 1;
  padding: var(--space-xl);
  background-color: var(--bg-secondary);
  overflow-y: auto;
  max-height: calc(100vh - 180px); /* Ensure height constraint for scrolling */
}

/* Custom scrollbar for WebKit browsers */
.content::-webkit-scrollbar {
  width: 8px;
}

.content::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 4px;
}

.content::-webkit-scrollbar-thumb:hover {
  background-color: #a8a8a8;
}

.content::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

/* Main content area */
main {
  min-height: calc(100vh - 180px);
}

/* Footer positioning */
.admin-layout .admin-footer {
  margin-top: auto;
  background: #fff;
  padding: 15px 0;
  border-top: 1px solid #e0e0e0;
}

/* Responsive Design for medium screens (1024px-1366px) */
@media (min-width: 1024px) and (max-width: 1366px) {
  .main-wrapper {
    margin-left: 280px; /* Adjust for smaller sidebar */
  }
}

/* Mobile and tablet responsive */
@media (max-width: 1024px) {
  .main-wrapper {
    margin-left: 0;
    padding-top: 60px; /* Space for mobile toggle button */
  }

  .content {
    padding: 1rem;
  }
}

@media (max-width: 768px) {
  .main-wrapper {
    padding-top: 50px; /* Adjust for smaller mobile toggle */
  }

  .content {
    padding: 0.75rem;
  }
}

/* Adjust main wrapper for collapsed sidebar on desktop */
@media (min-width: 1025px) {
  .main-wrapper.sidebar-collapsed {
    margin-left: 80px;
  }
}

/* Content Card Styles */
.content-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin-bottom: 24px;
}

/* Form Styles */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-control {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

/* ===== UNIFIED BUTTON SYSTEM ===== */
.btn {
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-md);
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  text-decoration: none;
  line-height: 1.5;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--text-inverse);
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: var(--text-inverse);
}

.btn-secondary:hover {
  background-color: var(--secondary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-success {
  background-color: var(--success-color);
  color: var(--text-inverse);
}

.btn-danger {
  background-color: var(--danger-color);
  color: var(--text-inverse);
}

.btn-sm {
  padding: var(--space-xs) var(--space-md);
  font-size: 0.75rem;
}

.btn-lg {
  padding: var(--space-md) var(--space-xl);
  font-size: 1rem;
}

/* Message Styles */
.success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
}

/* Remove the scrollbar hiding rules */

/* Firefox scrollbar styling */
.admin-layout .content {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

/* Prevent scroll on body when modal is open */
body.modal-open {
  overflow: hidden !important;
}

/* Notification container */
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 350px;
}

/* Animation for notifications */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.notification-container .alert-message {
  animation: slideIn 0.3s ease forwards;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
