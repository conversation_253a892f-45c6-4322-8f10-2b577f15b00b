/* ===== LEAGUE MANAGEMENT STYLES ===== */
/* Using unified CSS variables and design system */

.league-management {
  width: 100%;
  padding: var(--space-2xl);
  background-color: var(--bg-secondary);
}

/* Full width on large screens */
@media (min-width: 1200px) {
  .league-management {
    padding: var(--space-2xl) var(--space-lg);
  }
}

/* ===== UNIFIED TABLE HEADER STYLING ===== */
.league-management table thead th {
  background-color: var(--primary-color);
  color: var(--text-inverse);
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.league-management table thead tr {
  background-color: var(--primary-color);
}

.league-management table thead {
  background-color: var(--primary-color);
}

/* ===== USING UNIFIED LOADING SPINNER FROM INDEX.CSS ===== */

.page-header {
  margin-bottom: 2rem;
}

.page-header h1 {
  color: #000000;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.page-header h1 svg {
  color: #166534;
  font-size: 1.8rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

/* Removed view toggle controls */

/* ===== UNIFIED BUTTON STYLING ===== */
.create-league-btn {
  padding: var(--space-md) var(--space-xl);
  background-color: var(--primary-color);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-size: 0.875rem;
}

.create-league-btn:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.error-message {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1.5rem;
  border: 1px solid #fecaca;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.success-message {
  background-color: #dcfce7;
  color: #166534;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1.5rem;
  border: 1px solid #bbf7d0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #6b7280;
  font-size: 1.1rem;
}

.no-leagues {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px dashed #d1d5db;
  margin: 2rem 0;
}

/* List View Styles */
.leagues-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1.5rem;
}

.league-list {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.league-list:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.league-list-content {
  display: flex;
  align-items: center;
  padding: 1.25rem 1.5rem;
  gap: 1.5rem;
}

.league-list-main {
  display: flex;
  align-items: center;
  gap: 1.25rem;
  flex: 1;
}

.league-list-icon {
  width: 56px;
  height: 56px;
  border-radius: 10px;
  background-color: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  flex-shrink: 0;
  border: 2px solid #e5e7eb;
}

.league-list-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.league-list-icon svg {
  font-size: 1.75rem;
  color: #166534;
}

.league-list-info {
  flex: 1;
}

.league-list-info h3 {
  margin: 0 0 0.35rem 0;
  font-size: 1.25rem;
  color: #111827;
  font-weight: 600;
}

.league-list-info p {
  margin: 0 0 0.75rem 0;
  font-size: 0.95rem;
  color: #4b5563;
  line-height: 1.4;
}

.league-list-details {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.league-list-details span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #4b5563;
  background-color: #f9fafb;
  padding: 0.4rem 0.75rem;
  border-radius: 6px;
  border: 1px solid #f3f4f6;
}

.league-list-details svg {
  color: #166534;
}

.league-list-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  padding: 0 1.5rem;
  color: #4b5563;
  white-space: nowrap;
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #9ca3af;
}

.status-indicator.active {
  background-color: #10b981;
}

.status-indicator.upcoming {
  background-color: #3b82f6;
}

.status-indicator.completed {
  background-color: #6b7280;
}

.status-indicator.cancelled {
  background-color: #ef4444;
}

.league-list-actions {
  display: flex;
  gap: 0.75rem;
}

.league-list-actions button {
  background-color: #f3f4f6;
  border: none;
  border-radius: 8px;
  padding: 0 15px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  white-space: nowrap;
}

.league-list-actions button svg {
  margin-right: 6px;
  font-size: 1rem;
}

.league-list-actions button:hover {
  background-color: #e5e7eb;
  color: #111827;
  transform: translateY(-2px);
}

.league-list-actions button.edit-button {
  background-color: #0891b2;
  color: white;
}

.league-list-actions button.edit-button:hover {
  background-color: #0e7490;
}

.league-list-actions button.manage-button {
  background-color: #166534;
  color: white;
}

.league-list-actions button.manage-button:hover {
  background-color: #15803d;
}

/* Card view styles removed */

/* ===== LEAGUE MANAGEMENT MODAL STYLES ===== */
/* Using unified modal system from index.css */

/* League-specific modal customizations */
.modal-header h2 svg {
  color: var(--primary-color);
}

form {
  padding: 1.5rem;
}

.form-section {
  background-color: #f9fafb;
  border-radius: 10px;
  padding: 1.25rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.form-section h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  color: #166534;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-group {
  margin-bottom: 1.25rem;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.25rem;
}

.form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
  font-size: 0.95rem;
}

input[type="text"],
input[type="number"],
textarea,
select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background-color: white;
}

input[type="text"]:focus,
input[type="number"]:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: #166534;
  box-shadow: 0 0 0 3px rgba(22, 101, 52, 0.1);
}

input[type="file"] {
  padding: 0.5rem 0;
}

textarea {
  min-height: 100px;
  resize: vertical;
}

.media-preview-section {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.icon-preview-container,
.banner-preview-container {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  border: 2px dashed #d1d5db;
  transition: all 0.2s ease;
  cursor: pointer;
}

.icon-preview-container:hover,
.banner-preview-container:hover {
  border-color: #166534;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #6b7280;
  text-align: center;
}

.preview-placeholder svg {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: #9ca3af;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.color-preview-section {
  display: flex;
  align-items: center;
  gap: 1rem;
  background-color: white;
  padding: 0.75rem;
  border-radius: 8px;
}

.color-picker {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  background-color: var(--theme-color, #166534);
}

.color-value {
  font-family: monospace;
  font-size: 0.9rem;
  color: #4b5563;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.cancel-button {
  padding: 0.75rem 1.5rem;
  background-color: #f3f4f6;
  color: #4b5563;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button:hover {
  background-color: #e5e7eb;
}

.submit-button {
  padding: 0.75rem 1.5rem;
  background-color: #166534;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.submit-button:hover {
  background-color: #15803d;
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.submit-button:disabled,
.cancel-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .league-management {
    padding: 1.5rem;
  }

  .league-list-content {
    padding: 0.75rem 1rem;
  }

  .league-list-details {
    flex-direction: column;
    gap: 0.25rem;
  }
}

@media (max-width: 992px) {
  .modal-content {
    width: 95%;
    max-width: 700px;
  }

  .media-preview-section {
    grid-template-columns: 1fr;
  }

  .header-content {
    flex-wrap: wrap;
    gap: 1rem;
  }
}

/* Responsive Table Styles */
.league-table-show-mobile {
  display: none;
}

/* Responsive breakpoints for different sidebar states */
@media (min-width: 1200px) and (max-width: 1366px) {
  .league-table-hide-medium {
    display: none !important;
  }
}

@media (min-width: 1024px) and (max-width: 1199px) {
  .league-table-hide-medium {
    display: none !important;
  }
}

@media (max-width: 1024px) {
  .league-table-hide-small,
  .league-table-hide-medium {
    display: none !important;
  }

  .league-table-show-mobile {
    display: block !important;
  }
}

@media (max-width: 768px) {
  .league-management {
    padding: 1rem;
  }

  .page-header h1 {
    font-size: 1.5rem;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .create-league-btn {
    width: 100%;
    justify-content: center;
    margin-left: 0;
  }

  .form-row {
    flex-direction: column;
    gap: 1.25rem;
  }

  .form-section {
    padding: 1rem;
  }

  .modal-content {
    width: 95%;
    padding: 1.25rem;
  }

  /* Table responsive adjustments */
  table {
    font-size: 0.875rem;
  }

  th, td {
    padding: 0.5rem !important;
  }
}

@media (max-width: 576px) {
  .league-management {
    padding: 0.75rem;
  }

  .page-header h1 {
    font-size: 1.25rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .league-list-icon {
    width: 40px;
    height: 40px;
  }

  .league-list-info h3 {
    font-size: 1rem;
  }

  .league-list-info p {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .league-management {
    padding: 0.5rem;
  }

  .page-header h1 {
    font-size: 1.2rem;
  }

  .modal-content {
    width: 100%;
    max-width: none;
    border-radius: 0;
    height: 100%;
    max-height: 100%;
    padding: 1rem;
  }

  .modal-header {
    padding: 0.5rem 0.75rem;
    border-radius: 0;
  }

  .modal-header h2 {
    font-size: 1.1rem;
  }

  .league-list-details span {
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
  }

  .league-list-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .league-list-actions button {
    width: 100%;
    justify-content: center;
  }
}

/* Additional form styles already defined above */
