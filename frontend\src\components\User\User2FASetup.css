.user-2fa-setup {
    max-width: 800px;
    margin: 0 auto;
    padding: 24px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.setup-header {
    text-align: center;
    margin-bottom: 32px;
}

.back-btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 2px solid #e2e8f0;
    color: #4a5568;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    padding: 12px 20px;
    border-radius: 10px;
    transition: all 0.3s ease;
    margin-bottom: 20px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.back-btn:hover {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e0 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(148, 163, 184, 0.3);
    color: #2d3748;
}

.setup-header h1 {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    font-size: 28px;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 24px 0;
}

.setup-steps {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 16px;
}

.step {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 16px;
    background-color: #e2e8f0;
    color: #718096;
    transition: all 0.3s ease;
}

.step.active {
    background-color: #52B788;
    color: white;
}

.setup-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #52B788;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.setup-alert {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 24px;
    font-weight: 500;
}

.setup-alert-error {
    background-color: #fed7d7;
    color: #c53030;
    border: 1px solid #feb2b2;
}

.setup-alert-success {
    background-color: #c6f6d5;
    color: #2f855a;
    border: 1px solid #9ae6b4;
}

.setup-step {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 2px solid #e2e8f0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.step-header {
    padding: 32px 24px 20px 24px;
    border-bottom: 2px solid #e2e8f0;
    text-align: center;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.step-header h2 {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    font-size: 22px;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 8px 0;
}

.step-header p {
    color: #718096;
    font-size: 16px;
    margin: 0;
    line-height: 1.5;
}

.setup-content {
    padding: 32px 24px;
}

.qr-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 32px;
    align-items: start;
}

.qr-code-container {
    text-align: center;
}

.qr-code {
    max-width: 220px;
    width: 100%;
    height: auto;
    border: 3px solid #3b82f6;
    border-radius: 16px;
    padding: 20px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
    transition: all 0.3s ease;
}

.qr-code:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.3);
}

.manual-entry {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    padding: 24px;
    border-radius: 12px;
    border: 2px solid #0ea5e9;
    box-shadow: 0 2px 8px rgba(14, 165, 233, 0.15);
}

.manual-entry h3 {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 8px 0;
}

.manual-entry p {
    color: #718096;
    font-size: 14px;
    margin: 0 0 16px 0;
    line-height: 1.5;
}

.secret-key-container {
    display: flex;
    align-items: center;
    gap: 12px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    padding: 16px;
    border-radius: 8px;
    border: 2px solid #e2e8f0;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.secret-key-container:hover {
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.secret-key {
    flex: 1;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    color: #2d3748;
    word-break: break-all;
    background: none;
    border: none;
    padding: 0;
}

.copy-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    border: none;
    color: white;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-size: 14px;
    box-shadow: 0 2px 6px rgba(59, 130, 246, 0.3);
}

.copy-btn:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.setup-info {
    margin-top: 24px;
}

.info-card {
    display: flex;
    gap: 12px;
    background: #ebf8ff;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #3182ce;
}

.info-icon {
    color: #3182ce;
    font-size: 16px;
    margin-top: 2px;
    flex-shrink: 0;
}

.info-card h4 {
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 8px 0;
}

.info-card ol {
    margin: 0;
    padding-left: 16px;
    color: #4a5568;
    font-size: 14px;
    line-height: 1.5;
}

.verification-form {
    max-width: 300px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 8px;
}

.verification-input {
    width: 100%;
    padding: 16px 20px;
    border: 3px solid #e2e8f0;
    border-radius: 12px;
    font-size: 20px;
    font-weight: 700;
    text-align: center;
    letter-spacing: 6px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.verification-input:focus {
    outline: none;
    border-color: #3b82f6;
    background: linear-gradient(135deg, #ffffff 0%, #eff6ff 100%);
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2), 0 4px 12px rgba(59, 130, 246, 0.3);
    transform: translateY(-1px);
}

.backup-codes-section {
    max-width: 600px;
    margin: 0 auto;
}

.backup-codes-container {
    background: #f7fafc;
    padding: 24px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    margin-bottom: 20px;
}

.backup-codes-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.backup-codes-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.backup-codes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
}

.backup-code {
    background: white;
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    text-align: center;
    color: #2d3748;
}

.backup-warning {
    display: flex;
    gap: 12px;
    background: #fef5e7;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #f6ad55;
}

.warning-icon {
    color: #f6ad55;
    font-size: 16px;
    margin-top: 2px;
    flex-shrink: 0;
}

.backup-warning div {
    font-size: 14px;
    line-height: 1.5;
    color: #744210;
}

.backup-warning strong {
    color: #744210;
}

.step-actions {
    padding: 24px;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    gap: 12px;
}

.btn {
    padding: 14px 28px;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
}

.btn-primary:disabled {
    background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
    cursor: not-allowed;
    opacity: 0.7;
    transform: none;
    box-shadow: 0 2px 6px rgba(148, 163, 184, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #4a5568;
    border: 2px solid #e2e8f0;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e0 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(148, 163, 184, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(16, 185, 129, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
    .user-2fa-setup {
        padding: 16px;
    }
    
    .qr-section {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .setup-content {
        padding: 20px 16px;
    }
    
    .step-actions {
        flex-direction: column;
    }
    
    .backup-codes-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .setup-header h1 {
        font-size: 24px;
    }
    
    .step-header h2 {
        font-size: 20px;
    }
}
