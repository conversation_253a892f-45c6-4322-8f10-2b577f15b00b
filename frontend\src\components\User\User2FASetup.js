import React, { useState } from 'react';
import axios from 'axios';
import { 
    FaShieldAlt, 
    FaQrcode, 
    FaKey, 
    FaArrowLeft, 
    FaCheck,
    FaExclamationTriangle,
    FaCopy,
    FaDownload
} from 'react-icons/fa';
import './User2FASetup.css';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '/backend';

function User2FASetup({ userId, userEmail, onSuccess, onBack }) {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [step, setStep] = useState(1); // 1: Setup, 2: Verify, 3: Complete
    
    // 2FA Setup data
    const [qrCodeUrl, setQrCodeUrl] = useState('');
    const [secretKey, setSecretKey] = useState('');
    const [backupCodes, setBackupCodes] = useState([]);
    const [verificationCode, setVerificationCode] = useState('');

    const start2FASetup = async () => {
        try {
            setLoading(true);
            setError('');

            const response = await axios.post(
                `${API_BASE_URL}/handlers/user_setup_2fa.php`,
                {
                    userId: userId,
                    action: 'generate'
                }
            );

            if (response.data.success) {
                setQrCodeUrl(response.data.qr_code_url);
                setSecretKey(response.data.secret);
                setStep(2);
            } else {
                setError(response.data.message || 'Failed to generate 2FA setup');
            }
        } catch (err) {
            console.error('2FA setup error:', err);
            setError('Failed to start 2FA setup. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const verify2FACode = async () => {
        if (!verificationCode || verificationCode.length !== 6) {
            setError('Please enter a valid 6-digit code');
            return;
        }

        try {
            setLoading(true);
            setError('');

            const response = await axios.post(
                `${API_BASE_URL}/handlers/user_setup_2fa.php`,
                {
                    userId: userId,
                    action: 'verify',
                    code: verificationCode
                }
            );

            if (response.data.success) {
                setBackupCodes(response.data.backup_codes || []);
                setStep(3);
                setSuccess('2FA has been successfully enabled!');
            } else {
                setError(response.data.message || 'Invalid verification code');
            }
        } catch (err) {
            console.error('2FA verification error:', err);
            setError('Failed to verify code. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const complete2FASetup = () => {
        if (onSuccess) {
            onSuccess();
        }
    };

    const copyToClipboard = (text) => {
        navigator.clipboard.writeText(text).then(() => {
            setSuccess('Copied to clipboard!');
            setTimeout(() => setSuccess(''), 2000);
        });
    };

    const downloadBackupCodes = () => {
        const content = `FanBet247 2FA Backup Codes\n\nGenerated: ${new Date().toLocaleString()}\nUser: ${userEmail}\n\nBackup Codes:\n${backupCodes.join('\n')}\n\nKeep these codes safe! Each code can only be used once.`;
        
        const blob = new Blob([content], { type: 'text/plain' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'fanbet247-backup-codes.txt';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    };

    return (
        <div className="user-2fa-setup">
            {error && (
                <div className="setup-alert setup-alert-error">
                    <FaExclamationTriangle />
                    <span>{error}</span>
                </div>
            )}

            {success && (
                <div className="setup-alert setup-alert-success">
                    <FaCheck />
                    <span>{success}</span>
                </div>
            )}

            {/* Step 1: Introduction */}
            {step === 1 && (
                <div className="setup-step">
                    <div className="setup-header">
                        <button className="back-btn" onClick={onBack}>
                            <FaArrowLeft /> Back to Settings
                        </button>
                        <h2><FaShieldAlt /> Setup Two-Factor Authentication</h2>
                    </div>

                    <div className="setup-content">
                        <div className="setup-info">
                            <h3>Enhance Your Account Security</h3>
                            <p>Two-factor authentication adds an extra layer of security to your account by requiring a verification code from your mobile device in addition to your password.</p>
                            
                            <div className="setup-requirements">
                                <h4>What you'll need:</h4>
                                <ul>
                                    <li>A smartphone or tablet</li>
                                    <li>An authenticator app (Google Authenticator, Authy, etc.)</li>
                                    <li>A few minutes to complete the setup</li>
                                </ul>
                            </div>

                            <div className="setup-benefits">
                                <h4>Benefits:</h4>
                                <ul>
                                    <li>🔒 Enhanced account security</li>
                                    <li>🛡️ Protection against unauthorized access</li>
                                    <li>📱 Works offline with your mobile device</li>
                                    <li>🔑 Backup codes for emergency access</li>
                                </ul>
                            </div>
                        </div>

                        <div className="setup-actions">
                            <button 
                                className="btn btn-primary"
                                onClick={start2FASetup}
                                disabled={loading}
                            >
                                {loading ? 'Setting up...' : 'Start Setup'}
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* Step 2: QR Code and Verification */}
            {step === 2 && (
                <div className="setup-step">
                    <div className="setup-header">
                        <button className="back-btn" onClick={() => setStep(1)}>
                            <FaArrowLeft /> Back
                        </button>
                        <h2><FaQrcode /> Scan QR Code</h2>
                    </div>

                    <div className="setup-content">
                        <div className="qr-section">
                            <div className="qr-instructions">
                                <h3>Step 1: Scan the QR Code</h3>
                                <p>Open your authenticator app and scan this QR code:</p>
                            </div>

                            <div className="qr-code-container">
                                {qrCodeUrl && (
                                    <img src={qrCodeUrl} alt="2FA QR Code" className="qr-code" />
                                )}
                            </div>

                            <div className="manual-entry">
                                <h4>Can't scan? Enter manually:</h4>
                                <div className="secret-key">
                                    <code>{secretKey}</code>
                                    <button 
                                        className="copy-btn"
                                        onClick={() => copyToClipboard(secretKey)}
                                    >
                                        <FaCopy />
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div className="verification-section">
                            <h3>Step 2: Enter Verification Code</h3>
                            <p>Enter the 6-digit code from your authenticator app:</p>
                            
                            <div className="code-input-container">
                                <input
                                    type="text"
                                    className="code-input"
                                    placeholder="000000"
                                    value={verificationCode}
                                    onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                                    maxLength="6"
                                />
                            </div>

                            <button 
                                className="btn btn-primary"
                                onClick={verify2FACode}
                                disabled={loading || verificationCode.length !== 6}
                            >
                                {loading ? 'Verifying...' : 'Verify & Enable 2FA'}
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* Step 3: Backup Codes */}
            {step === 3 && (
                <div className="setup-step">
                    <div className="setup-header">
                        <h2><FaKey /> Save Your Backup Codes</h2>
                    </div>

                    <div className="setup-content">
                        <div className="backup-codes-section">
                            <div className="backup-info">
                                <h3>🎉 2FA Successfully Enabled!</h3>
                                <p>Save these backup codes in a safe place. You can use them to access your account if you lose your phone.</p>
                                <p className="backup-warning">
                                    <strong>Important:</strong> Each code can only be used once. Store them securely!
                                </p>
                            </div>

                            <div className="backup-codes">
                                {backupCodes.map((code, index) => (
                                    <div key={index} className="backup-code">
                                        <code>{code}</code>
                                    </div>
                                ))}
                            </div>

                            <div className="backup-actions">
                                <button 
                                    className="btn btn-secondary"
                                    onClick={downloadBackupCodes}
                                >
                                    <FaDownload /> Download Codes
                                </button>
                                <button 
                                    className="btn btn-secondary"
                                    onClick={() => copyToClipboard(backupCodes.join('\n'))}
                                >
                                    <FaCopy /> Copy All Codes
                                </button>
                            </div>

                            <div className="completion-actions">
                                <button 
                                    className="btn btn-primary"
                                    onClick={complete2FASetup}
                                >
                                    Complete Setup
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}

export default User2FASetup;
