import React from 'react';
import { useNavigate } from 'react-router-dom';
import './LiveMatchCard.css';

const LiveMatchCard = ({ match, onBetClick }) => {
    const navigate = useNavigate();

    const getTeamLogo = (teamName) => {
        if (!teamName) return '/images/default-team.png';
        return `/backend/uploads/teams/${teamName.toLowerCase().replace(/\s+/g, '_')}.png`;
    };

    const getLeagueIcon = (leagueName) => {
        const leagueIcons = {
            'Premier League': 'https://media.api-sports.io/football/leagues/39.png',
            'La Liga': 'https://media.api-sports.io/football/leagues/140.png',
            'Bundesliga': 'https://media.api-sports.io/football/leagues/78.png',
            'Serie A': 'https://media.api-sports.io/football/leagues/135.png',
            'Ligue 1': 'https://media.api-sports.io/football/leagues/61.png',
            'Champions League': 'https://media.api-sports.io/football/leagues/1.png',
        };
        return leagueIcons[leagueName] || '/images/default-league.png';
    };

    const handleBetClick = (betType, odds) => {
        if (onBetClick) {
            onBetClick({
                matchId: match.challenge_id,
                teamA: match.team_a,
                teamB: match.team_b,
                betType,
                odds,
                match
            });
        } else {
            // Navigate to challenge page if no handler provided
            navigate(`/user/join-challenge/${match.challenge_id}`);
        }
    };

    const formatTime = (seconds) => {
        if (!seconds || seconds <= 0) return "90'";
        const minutes = Math.floor(Math.random() * 90) + 1;
        return `${minutes}'`;
    };

    const generateScore = () => {
        return {
            teamA: Math.floor(Math.random() * 4),
            teamB: Math.floor(Math.random() * 4)
        };
    };

    const score = generateScore();

    return (
        <div className="live-match-card">
            <div className="match-header">
                <div className="league-info">
                    <img 
                        src={getLeagueIcon('Premier League')} 
                        alt="League" 
                        className="league-icon-small"
                    />
                    <span>Premier League</span>
                </div>
                <div className="match-time">
                    {formatTime(match.seconds_remaining)}
                </div>
            </div>
            
            <div className="match-teams">
                <div className="team">
                    <img 
                        src={getTeamLogo(match.team_a)} 
                        alt={match.team_a}
                        className="team-logo"
                        onError={(e) => {
                            e.target.src = '/images/default-team.png';
                        }}
                    />
                    <span className="team-name">{match.team_a}</span>
                </div>
                
                <div className="match-score">
                    <div className="score">
                        {score.teamA} - {score.teamB}
                    </div>
                    <div className="status">Live</div>
                </div>
                
                <div className="team">
                    <img 
                        src={getTeamLogo(match.team_b)} 
                        alt={match.team_b}
                        className="team-logo"
                        onError={(e) => {
                            e.target.src = '/images/default-team.png';
                        }}
                    />
                    <span className="team-name">{match.team_b}</span>
                </div>
            </div>
            
            <div className="betting-odds">
                <button 
                    className="odds-btn home"
                    onClick={() => handleBetClick('team_a', match.odds_team_a)}
                    title={`Bet on ${match.team_a} to win`}
                >
                    <span className="odds-label">1</span>
                    <span className="odds-value">{match.odds_team_a}</span>
                </button>
                <button 
                    className="odds-btn draw"
                    onClick={() => handleBetClick('draw', match.odds_draw)}
                    title="Bet on draw"
                >
                    <span className="odds-label">X</span>
                    <span className="odds-value">{match.odds_draw}</span>
                </button>
                <button 
                    className="odds-btn away"
                    onClick={() => handleBetClick('team_b', match.odds_team_b)}
                    title={`Bet on ${match.team_b} to win`}
                >
                    <span className="odds-label">2</span>
                    <span className="odds-value">{match.odds_team_b}</span>
                </button>
            </div>
            
            {match.isLive && (
                <div className="live-indicator-bottom">
                    <span className="pulse-dot"></span>
                    <span>LIVE</span>
                </div>
            )}
        </div>
    );
};

export default LiveMatchCard;
