/* ===== USER MANAGEMENT TABLE SYSTEM ===== */
/* Using unified CSS variables and design system */

.user-table-container {
    overflow-x: auto;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    background: var(--bg-primary);
}

.user-table {
    min-width: 100%;
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.user-table thead {
    background: var(--primary-color);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
}

.user-table th,
.user-table td {
    text-align: left;
    vertical-align: middle;
    border-bottom: 1px solid var(--border-light);
    padding: var(--space-md) var(--space-lg);
}

.user-table th {
    background: var(--primary-color);
    color: var(--text-inverse);
    font-weight: 600;
    font-size: 0.875rem;
    white-space: nowrap;
}

.user-table tbody tr:hover {
    background-color: var(--bg-tertiary);
}

.user-table tbody tr:hover .user-table-actions {
    background: var(--bg-tertiary);
}

/* ===== FIXED STICKY ACTIONS COLUMN ===== */
.user-table-actions {
    position: sticky;
    right: 0;
    background: var(--bg-primary);
    z-index: var(--z-sticky);
    box-shadow: -2px 0 4px rgba(0, 0, 0, 0.05);
    min-width: 160px;
    width: 160px;
    padding-right: var(--space-sm);
}

.user-table-actions .flex {
    justify-content: flex-start;
    gap: var(--space-xs);
    flex-wrap: nowrap;
}

.user-table-actions button {
    flex-shrink: 0;
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    font-size: 0.75rem;
}

/* Action button variants */
.user-table-actions .btn-primary {
    background-color: var(--primary-color);
    color: var(--text-inverse);
}

.user-table-actions .btn-primary:hover {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
}

.user-table-actions .btn-danger {
    background-color: var(--danger-color);
    color: var(--text-inverse);
}

.user-table-actions .btn-danger:hover {
    background-color: var(--danger-hover, #b91c1c);
    transform: translateY(-1px);
}

/* Sports Card Modal Styles */
.sports-card-modal {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 16px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    overflow-y: auto;
    max-height: 90vh;
    position: relative;
}

.sports-card-header {
    background: linear-gradient(135deg, #166534 0%, #15803d 100%);
    color: white;
    padding: 2rem;
    text-align: center;
    position: relative;
}

.sports-card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="2"/><circle cx="50" cy="50" r="25" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></svg>') center/cover;
    opacity: 0.3;
}

.sports-card-avatar {
    position: relative;
    z-index: 2;
    width: 120px;
    height: 120px;
    margin: 0 auto 1rem;
    border-radius: 50%;
    border: 4px solid white;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.sports-card-avatar img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 50%;
}

.sports-card-name {
    position: relative;
    z-index: 2;
    font-size: 1.75rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Header Content Layout */
.sports-card-header-content {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.sports-card-info {
    text-align: center;
    width: 100%;
}

.sports-card-name {
    position: relative;
    z-index: 2;
    font-size: 1.75rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.sports-card-username {
    position: relative;
    z-index: 2;
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0.75rem;
    font-weight: 500;
}

.sports-card-status {
    position: relative;
    z-index: 2;
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    backdrop-filter: blur(10px);
}

.sports-card-status.status-active {
    background: rgba(34, 197, 94, 0.9);
    color: white;
}

.sports-card-status.status-suspended {
    background: rgba(245, 158, 11, 0.9);
    color: white;
}

.sports-card-status.status-banned {
    background: rgba(239, 68, 68, 0.9);
    color: white;
}

.sports-card-team {
    position: relative;
    z-index: 2;
    font-size: 0.9rem;
    opacity: 0.85;
    font-weight: 500;
}

.team-label {
    font-weight: 600;
    opacity: 0.7;
}

.sports-card-body {
    padding: 1.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #166534;
}

.stat-card-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #166534;
}

.stat-card-icon {
    color: #166534;
    font-size: 1.2rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: #6b7280;
    font-weight: 500;
}

.stat-value {
    font-weight: 600;
    color: #111827;
}

.stat-value.positive {
    color: #059669;
}

.stat-value.negative {
    color: #dc2626;
}

.stat-value.neutral {
    color: #d97706;
}

/* Performance Stats Grid */
.performance-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin: 1.5rem 0;
}

.performance-stat {
    text-align: center;
    padding: 1rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.performance-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.performance-label {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Compact Stats Section */
.compact-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.compact-stat-card {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
}

.compact-stat-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #166534;
}

.compact-stat-title {
    font-size: 0.95rem;
    font-weight: 600;
    color: #166534;
    margin-right: auto;
}

.compact-stat-icon {
    color: #166534;
    font-size: 1rem;
}

.compact-stats-row {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
}

.compact-stat-item {
    display: flex;
    flex-direction: column;
    padding: 0.5rem;
    background: #f8fafc;
    border-radius: 4px;
}

.compact-stat-label {
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 500;
    margin-bottom: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.compact-stat-value {
    font-size: 0.9rem;
    font-weight: 600;
    color: #111827;
}

.compact-stat-value.positive {
    color: #059669;
}

.compact-stat-value.negative {
    color: #dc2626;
}

.compact-stat-value.neutral {
    color: #d97706;
}

/* User Info Summary */
.user-info-summary {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
}

.user-info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.4rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.user-info-row:last-child {
    border-bottom: none;
}

.user-info-label {
    font-size: 0.85rem;
    color: #6b7280;
    font-weight: 500;
}

.user-info-value {
    font-size: 0.85rem;
    font-weight: 600;
    color: #111827;
    text-align: right;
    max-width: 60%;
    word-break: break-word;
}

/* Responsive Design */
@media (min-width: 1024px) and (max-width: 1366px) {
    .user-table-hide-medium {
        display: none !important;
    }

    .user-table {
        min-width: 800px;
    }

    .user-table-actions {
        min-width: 140px;
        width: 140px;
    }

    .user-table-actions button {
        min-width: 28px;
        height: 28px;
        font-size: 0.875rem;
    }
}

@media (max-width: 1024px) {
    .user-table-hide-small,
    .user-table-hide-medium {
        display: none !important;
    }

    .user-table {
        min-width: 600px;
    }

    .user-table-actions {
        min-width: 120px;
        width: 120px;
    }

    .user-table-actions button {
        min-width: 26px;
        height: 26px;
        font-size: 0.8rem;
    }

    .user-table-actions .flex {
        gap: 2px;
    }

    .performance-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .compact-stats-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .compact-stats-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
}

@media (max-width: 768px) {
    .user-table-hide-very-small,
    .user-table-hide-small,
    .user-table-hide-medium {
        display: none !important;
    }

    .user-table {
        min-width: 480px;
    }

    .user-table-actions {
        min-width: 100px;
        width: 100px;
    }

    .user-table-actions button {
        min-width: 22px;
        height: 22px;
        font-size: 0.75rem;
    }

    .user-table-actions .flex {
        gap: 1px;
    }

    .sports-card-modal {
        margin: 0.5rem;
        max-height: calc(100vh - 1rem);
        max-width: calc(100vw - 1rem);
    }

    .sports-card-header {
        padding: 1rem;
    }

    .sports-card-avatar {
        width: 80px;
        height: 80px;
    }

    .sports-card-name {
        font-size: 1.25rem;
    }

    .sports-card-username {
        font-size: 0.95rem;
    }

    .sports-card-status {
        font-size: 0.75rem;
        padding: 0.4rem 0.8rem;
    }

    .sports-card-team {
        font-size: 0.8rem;
    }

    .sports-card-body {
        padding: 1rem;
    }

    .performance-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }

    .compact-stats-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .compact-stat-card {
        padding: 0.75rem;
    }

    .compact-stats-row {
        grid-template-columns: 1fr;
        gap: 0.25rem;
    }

    .user-info-summary {
        padding: 0.75rem;
    }
}

/* Ensure Actions column is always visible and sticky */
@media (max-width: 1600px) {
    .user-table-actions {
        position: sticky;
        right: 0;
        background: white;
        z-index: 10;
    }
}

/* Loading animation for modal */
.loading-spinner {
    border: 3px solid #f3f4f6;
    border-top: 3px solid #166534;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
