/* LiveMatchCard.css */

.live-match-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    cursor: pointer;
    position: relative;
}

.live-match-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.match-header {
    background: var(--primary-green);
    color: var(--white);
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.league-info {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
}

.league-icon-small {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    object-fit: contain;
}

.match-time {
    background: var(--white);
    color: var(--primary-green);
    padding: 4px 8px;
    border-radius: var(--border-radius);
    font-size: 12px;
    font-weight: 700;
    animation: pulse 2s infinite;
}

.match-teams {
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.team {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 0;
}

.team-logo {
    width: 48px;
    height: 48px;
    object-fit: contain;
    border-radius: 4px;
    transition: var(--transition);
}

.team-logo:hover {
    transform: scale(1.1);
}

.team-name {
    font-weight: 600;
    font-size: 14px;
    text-align: center;
    color: var(--dark-gray);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.match-score {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 0 20px;
    min-width: 80px;
}

.score {
    font-size: 24px;
    font-weight: 800;
    background: var(--light-gray);
    padding: 8px 16px;
    border-radius: var(--border-radius);
    color: var(--dark-gray);
    min-width: 60px;
    text-align: center;
}

.status {
    font-size: 12px;
    color: var(--gray);
    font-weight: 500;
    text-transform: uppercase;
}

.betting-odds {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 8px;
    padding: 20px;
    border-top: 1px solid var(--light-gray);
}

.odds-btn {
    background: var(--light-gray);
    border: none;
    padding: 12px 8px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    position: relative;
    overflow: hidden;
}

.odds-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.odds-btn:hover::before {
    left: 100%;
}

.odds-btn:hover {
    transform: scale(1.05);
}

.odds-btn:active {
    transform: scale(0.98);
}

.odds-btn.home {
    background: var(--light-green);
    color: var(--primary-green);
    border: 2px solid transparent;
}

.odds-btn.home:hover {
    background: var(--primary-green);
    color: var(--white);
    border-color: var(--secondary-green);
}

.odds-btn.draw {
    background: #f5f5f5;
    color: var(--gray);
    border: 2px solid transparent;
}

.odds-btn.draw:hover {
    background: var(--gray);
    color: var(--white);
    border-color: var(--dark-gray);
}

.odds-btn.away {
    background: #e3f2fd;
    color: #1976d2;
    border: 2px solid transparent;
}

.odds-btn.away:hover {
    background: #1976d2;
    color: var(--white);
    border-color: #1565c0;
}

.odds-label {
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.odds-value {
    font-size: 16px;
    font-weight: 800;
}

.live-indicator-bottom {
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--danger);
    color: var(--white);
    padding: 4px 8px;
    border-radius: var(--border-radius);
    font-size: 10px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 4px;
    z-index: 10;
}

.pulse-dot {
    width: 6px;
    height: 6px;
    background: var(--white);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
    100% { opacity: 1; transform: scale(1); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .match-teams {
        padding: 15px;
    }
    
    .team-logo {
        width: 36px;
        height: 36px;
    }
    
    .team-name {
        font-size: 12px;
    }
    
    .score {
        font-size: 20px;
        padding: 6px 12px;
    }
    
    .betting-odds {
        padding: 15px;
        gap: 6px;
    }
    
    .odds-btn {
        padding: 10px 6px;
    }
    
    .odds-value {
        font-size: 14px;
    }
    
    .odds-label {
        font-size: 11px;
    }
}

@media (max-width: 480px) {
    .match-header {
        padding: 10px 15px;
    }
    
    .league-info {
        font-size: 12px;
    }
    
    .match-teams {
        padding: 12px;
    }
    
    .team-logo {
        width: 32px;
        height: 32px;
    }
    
    .team-name {
        font-size: 11px;
    }
    
    .match-score {
        padding: 0 10px;
    }
    
    .score {
        font-size: 18px;
        padding: 4px 8px;
        min-width: 50px;
    }
    
    .betting-odds {
        padding: 12px;
        gap: 4px;
    }
    
    .odds-btn {
        padding: 8px 4px;
    }
    
    .odds-value {
        font-size: 13px;
    }
    
    .odds-label {
        font-size: 10px;
    }
}
