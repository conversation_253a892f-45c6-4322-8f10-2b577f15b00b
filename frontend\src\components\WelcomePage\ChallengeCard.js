import React from 'react';
import { useNavigate } from 'react-router-dom';
import './ChallengeCard.css';

const ChallengeCard = ({ challenge, index }) => {
    const navigate = useNavigate();

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    };

    const generateChallengeData = () => {
        const challengeTypes = [
            {
                title: "Predict 5 Correct Match Results",
                description: "Select matches from Premier League this weekend for a chance to win!",
                participants: Math.floor(Math.random() * 2000) + 500,
                prizePool: Math.floor(Math.random() * 10000) + 1000,
                userProgress: `${Math.floor(Math.random() * 5)}/5`,
                timeLeft: "2d 5h",
                badge: "NEW",
                badgeType: "new"
            },
            {
                title: "Perfect Parlay Challenge",
                description: "Create a parlay with 3+ selections and win bonus cash if all correct!",
                participants: Math.floor(Math.random() * 3000) + 1000,
                prizePool: Math.floor(Math.random() * 15000) + 5000,
                userProgress: "Active",
                timeLeft: "1d 12h",
                badge: "POPULAR",
                badgeType: "popular"
            },
            {
                title: "Weekend Warrior",
                description: "Bet on all weekend matches and earn multiplier bonuses!",
                participants: Math.floor(Math.random() * 1500) + 300,
                prizePool: Math.floor(Math.random() * 8000) + 2000,
                userProgress: `${Math.floor(Math.random() * 8)}/8`,
                timeLeft: "3d 8h",
                badge: "HOT",
                badgeType: "hot"
            },
            {
                title: "Champions League Special",
                description: "Predict Champions League outcomes for exclusive rewards!",
                participants: Math.floor(Math.random() * 5000) + 2000,
                prizePool: Math.floor(Math.random() * 25000) + 10000,
                userProgress: `${Math.floor(Math.random() * 4)}/4`,
                timeLeft: "5d 2h",
                badge: "VIP",
                badgeType: "vip"
            }
        ];

        return challengeTypes[index % challengeTypes.length];
    };

    const challengeData = generateChallengeData();

    const handleParticipate = () => {
        if (challenge.bet_id) {
            navigate(`/user/join-challenge/${challenge.bet_id}`);
        } else {
            // For demo challenges, navigate to challenges page
            navigate('/user/challenges');
        }
    };

    const getBadgeClass = (badgeType) => {
        const classes = {
            new: 'badge new',
            popular: 'badge popular',
            hot: 'badge hot',
            vip: 'badge vip'
        };
        return classes[badgeType] || 'badge new';
    };

    return (
        <div className="challenge-card">
            <div className="challenge-header">
                <div className="challenge-badge">
                    <span className={getBadgeClass(challengeData.badgeType)}>
                        {challengeData.badge}
                    </span>
                    <span className="time-left">Ends in {challengeData.timeLeft}</span>
                </div>
            </div>
            
            <div className="challenge-content">
                <h3>{challengeData.title}</h3>
                <p>{challengeData.description}</p>
                
                <div className="challenge-stats">
                    <div className="stat">
                        <span className="label">Participants</span>
                        <span className="value">{challengeData.participants.toLocaleString()}</span>
                    </div>
                    <div className="stat">
                        <span className="label">Prize Pool</span>
                        <span className="value prize">{formatCurrency(challengeData.prizePool)}</span>
                    </div>
                    <div className="stat">
                        <span className="label">Your Entry</span>
                        <span className="value progress">{challengeData.userProgress}</span>
                    </div>
                </div>
                
                <div className="challenge-progress">
                    <div className="progress-bar">
                        <div 
                            className="progress-fill" 
                            style={{ width: `${Math.floor(Math.random() * 80) + 20}%` }}
                        ></div>
                    </div>
                    <span className="progress-text">
                        {Math.floor(Math.random() * 80) + 20}% Complete
                    </span>
                </div>
            </div>
            
            <div className="challenge-footer">
                <button 
                    className="participate-btn"
                    onClick={handleParticipate}
                >
                    {challengeData.userProgress === "Active" ? "View Details" : "Participate Now"}
                </button>
            </div>
            
            <div className="challenge-decoration">
                <div className="decoration-circle"></div>
                <div className="decoration-triangle"></div>
            </div>
        </div>
    );
};

export default ChallengeCard;
